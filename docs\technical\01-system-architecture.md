# System Architecture

*Comprehensive overview of the Journeyman Jobs application architecture*

## 🏗️ Architecture Overview

Journeyman Jobs follows a **layered mobile-first architecture** designed specifically for electrical workers who need reliable, performant access to job opportunities in various field conditions.

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │     Screens     │  │     Widgets     │  │  Electrical  │ │
│  │   (UI Pages)    │  │  (Reusable UI)  │  │ Components   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   State Management Layer                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Providers     │  │   App State     │  │   Navigation │ │
│  │ (ChangeNotifier)│  │  (Global State) │  │  (go_router) │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     Business Logic Layer                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │    Services     │  │   Use Cases     │  │   Models     │ │
│  │ (API Calls,etc) │  │(Business Rules) │  │ (Data DTOs)  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      Data Access Layer                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Repositories  │  │     Cache       │  │   External   │ │
│  │ (Data Sources)  │  │  (Local Store)  │  │     APIs     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                       Backend Services                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │    Firebase     │  │      NOAA       │  │    Google    │ │
│  │  (Auth/DB/FCM)  │  │   (Weather)     │  │    Maps      │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📱 Frontend Architecture

### Flutter Application Structure

The application follows Flutter's **feature-based architecture** with electrical industry-specific adaptations:

```
lib/
├── main.dart                     # App entry point
├── design_system/               # Electrical theme system
│   ├── app_theme.dart           # Navy/copper color scheme
│   ├── components/              # Reusable UI components
│   └── illustrations/           # Electrical worker illustrations
├── electrical_components/       # Industry-specific UI components
│   ├── circuit_breaker_toggle.dart
│   ├── electrical_loader.dart
│   ├── power_line_loader.dart
│   └── transformer_trainer/     # Advanced electrical training components
├── screens/                     # Feature screens organized by domain
│   ├── auth/                    # Authentication flows
│   ├── home/                    # Dashboard and overview
│   ├── jobs/                    # Job discovery and applications
│   ├── locals/                  # IBEW union directory
│   ├── storm/                   # Emergency response features
│   └── settings/                # Configuration and profile
├── providers/                   # State management (Provider pattern)
│   ├── app_state_provider.dart  # Global application state
│   ├── auth_provider.dart       # Authentication state
│   └── job_filter_provider.dart # Job filtering state
├── services/                    # Business logic and external integrations
│   ├── auth_service.dart        # Firebase authentication
│   ├── firestore_service.dart   # Firestore data operations
│   ├── noaa_weather_service.dart # Weather radar integration
│   └── notification_service.dart # Push notifications
├── models/                      # Data transfer objects
│   ├── job_model.dart           # Job posting structure
│   ├── user_model.dart          # User profile structure
│   └── locals_record.dart       # IBEW local union data
├── navigation/                  # Routing configuration
│   └── app_router.dart          # go_router setup
└── utils/                       # Helper functions and extensions
    ├── error_handling.dart      # Centralized error management
    ├── job_formatting.dart      # Electrical job data formatting
    └── enum_utils.dart          # IBEW classification enums
```

### Layer Responsibilities

#### **Presentation Layer**
- **Screens**: Full-page UI components representing app features
- **Widgets**: Reusable UI components with electrical theming
- **Electrical Components**: Industry-specific interactive elements

#### **State Management Layer**
- **Providers**: ChangeNotifier-based state management for reactive UI updates
- **App State**: Global application state including user session and preferences
- **Navigation**: Declarative routing with authentication guards

#### **Business Logic Layer**
- **Services**: Encapsulate external API calls and complex business logic
- **Use Cases**: Domain-specific business rules and data transformation
- **Models**: Immutable data structures representing business entities

#### **Data Access Layer**
- **Repositories**: Abstract data sources and provide unified interfaces
- **Cache**: Local storage for offline capability and performance
- **External APIs**: Integration adapters for third-party services

## 🔥 Backend Architecture

### Firebase Services Integration

```
Firebase Ecosystem
├── Authentication
│   ├── Email/Password
│   ├── Google Sign-In
│   └── Apple Sign-In
├── Cloud Firestore
│   ├── /users/{uid}           # User profiles
│   ├── /jobs/{jobId}          # Job postings
│   ├── /locals/{localId}      # IBEW union data
│   └── /notifications/{id}    # Push notification logs
├── Cloud Storage
│   ├── /profiles/             # User profile images
│   ├── /documents/            # Certifications and documents
│   └── /assets/               # App assets and illustrations
├── Cloud Messaging (FCM)
│   ├── Job alerts
│   ├── Storm work notifications
│   └── Application updates
└── Analytics
    ├── User behavior tracking
    ├── Job search patterns
    └── Performance monitoring
```

### Data Models and Relationships

```mermaid
erDiagram
    USER ||--o{ JOB_APPLICATION : applies
    USER ||--o{ SAVED_JOB : saves
    USER }o--|| LOCAL : belongs_to
    JOB }o--|| LOCAL : posted_by
    JOB ||--o{ JOB_APPLICATION : receives
    LOCAL ||--o{ JOB : posts
    
    USER {
        string uid PK
        string email
        string firstName
        string lastName
        int homeLocal FK
        enum classification
        datetime createdTime
    }
    
    JOB {
        string id PK
        int local FK
        string company
        string location
        string classification
        double wage
        datetime timestamp
    }
    
    LOCAL {
        int localNumber PK
        string name
        string city
        string state
        string phone
        string website
    }
```

## 🌐 External Integrations

### NOAA Weather Services
- **Radar Data**: Real-time weather radar for storm tracking
- **Alerts**: Severe weather warnings for electrical workers
- **Safety Integration**: Weather-based safety recommendations

### Google Services
- **Maps**: Location-based job searching and navigation
- **Places**: Address validation and autocomplete
- **Analytics**: User behavior and performance tracking

### Apple Services
- **Sign-In**: Secure authentication for iOS users
- **Push Notifications**: Native iOS notification delivery

## 🔄 Data Flow Patterns

### Typical User Action Flow

```
User Action (UI) 
    ↓
Provider (State Management)
    ↓
Service (Business Logic)
    ↓
Repository (Data Access)
    ↓
Firebase/External API
    ↓
Response Processing
    ↓
State Update
    ↓
UI Rebuild (Reactive)
```

### Example: Job Search Flow

```dart
// 1. User initiates search from JobsScreen
onSearch(String query) {
  context.read<JobFilterProvider>().updateSearchQuery(query);
}

// 2. Provider updates state and triggers service call
class JobFilterProvider extends ChangeNotifier {
  void updateSearchQuery(String query) {
    _searchQuery = query;
    _fetchJobs();
    notifyListeners();
  }
}

// 3. Service handles business logic and API calls
class JobService {
  Future<List<Job>> searchJobs(FilterCriteria criteria) async {
    final query = _buildFirestoreQuery(criteria);
    final snapshot = await query.get();
    return snapshot.docs.map((doc) => Job.fromFirestore(doc)).toList();
  }
}

// 4. UI rebuilds automatically with new data
Consumer<JobFilterProvider>(
  builder: (context, provider, child) {
    return ListView.builder(
      itemCount: provider.filteredJobs.length,
      itemBuilder: (context, index) => JobCard(job: provider.filteredJobs[index]),
    );
  },
)
```

## 🏭 IBEW-Specific Architecture Decisions

### Industry Context Integration

**Electrical Worker Classifications**
- Inside Wireman (commercial/industrial)
- Journeyman Lineman (transmission/distribution)
- Tree Trimmer (vegetation management)
- Equipment Operator (heavy machinery)

**Union Structure Awareness**
- 797+ IBEW local unions across North America
- Local-specific referral rules and procedures
- Book numbers and classification systems

**Mobile-First for Field Workers**
- Offline capability for remote job sites
- Battery-efficient animations and operations
- High contrast electrical theme for outdoor visibility
- One-handed operation optimization

### Performance Considerations

**Network Resilience**
- Automatic retry mechanisms for poor connectivity
- Intelligent caching of critical data (union directory)
- Graceful degradation when services are unavailable

**Battery Optimization**
- Background processing limitations
- Efficient animation frame rates
- Strategic use of device sensors (GPS, camera)

**Memory Management**
- Large dataset pagination (797+ locals, thousands of jobs)
- Image compression and caching
- Proper disposal of animation controllers

## 🔒 Security Architecture

### Authentication Security
- Firebase Authentication with industry-standard security
- Multi-provider support (Email, Google, Apple)
- Secure token handling and automatic refresh

### Data Security
- Firestore security rules enforce user data isolation
- Personal information encryption at rest and in transit
- GDPR-compliant data handling procedures

### Communication Security
- HTTPS for all external API communications
- Certificate pinning for critical service connections
- Input validation and sanitization

## 📊 Monitoring and Analytics

### Performance Monitoring
- Firebase Performance Monitoring for app metrics
- Custom electrical worker-specific analytics
- Real-time error tracking and crash reporting

### Business Analytics
- Job search pattern analysis
- User engagement with electrical features
- Union participation and adoption metrics

### Operational Monitoring
- API response time tracking
- Service availability monitoring
- User feedback and rating analysis

---

*This architecture supports the unique needs of electrical workers while maintaining scalability and maintainability for long-term growth.*