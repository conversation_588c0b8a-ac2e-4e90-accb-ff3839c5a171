# Development Setup Guide

*Complete development environment setup for the Journeyman Jobs application*

## 🚀 Prerequisites

### Required Software

**Flutter Development Kit**
```bash
# Install Flutter SDK (version 3.6.0 or higher)
# Download from: https://flutter.dev/docs/get-started/install

# Verify installation
flutter --version
flutter doctor -v
```

**Development Tools**
- **Git**: Version control system
- **Android Studio**: Android development and emulator
- **Xcode**: iOS development (macOS only)
- **VS Code**: Recommended code editor with Flutter extensions

**Firebase CLI**
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Verify installation
firebase --version
```

### Development Environment

**System Requirements**
- **OS**: macOS, Windows 10+, or Linux
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB free space for development tools
- **Network**: Stable internet connection for Firebase services

## 🔧 Project Setup

### 1. Repository Clone

```bash
# Clone the repository
git clone https://github.com/your-org/journeyman-jobs.git
cd journeyman-jobs

# Checkout main branch
git checkout main

# Pull latest changes
git pull origin main
```

### 2. Flutter Configuration

```bash
# Get Flutter dependencies
flutter pub get

# Generate code (if needed)
dart run build_runner build

# Verify Flutter setup
flutter doctor
```

**Expected `flutter doctor` output:**
```
[✓] Flutter (Channel stable, 3.6.0)
[✓] Android toolchain - develop for Android devices
[✓] Xcode - develop for iOS and macOS
[✓] Chrome - develop for the web
[✓] Android Studio (version 2023.1)
[✓] VS Code (version 1.85)
[✓] Connected device (2 available)
[✓] Network resources
```

### 3. Firebase Configuration

**Download Configuration Files**
1. Navigate to [Firebase Console](https://console.firebase.google.com/)
2. Select the Journeyman Jobs project
3. Download configuration files:
   - **Android**: `google-services.json` → `android/app/`
   - **iOS**: `GoogleService-Info.plist` → `ios/Runner/`

**Initialize Firebase**
```bash
# Initialize Firebase in project
firebase init

# Select these features:
# - Firestore: Database rules
# - Storage: Security rules
# - Hosting: Static file hosting

# Use existing project: journeyman-jobs-prod
```

### 4. Environment Variables

Create environment-specific configuration files:

**`.env.development`**
```env
# Development environment
FLUTTER_ENV=development
FIREBASE_PROJECT_ID=journeyman-jobs-dev
NOAA_API_BASE_URL=https://api.weather.gov
GOOGLE_MAPS_API_KEY=your_dev_maps_key
SENTRY_DSN=your_dev_sentry_dsn
```

**`.env.production`**
```env
# Production environment
FLUTTER_ENV=production
FIREBASE_PROJECT_ID=journeyman-jobs-prod
NOAA_API_BASE_URL=https://api.weather.gov
GOOGLE_MAPS_API_KEY=your_prod_maps_key
SENTRY_DSN=your_prod_sentry_dsn
```

## 🛠️ IDE Configuration

### VS Code Setup (Recommended)

**Required Extensions**
```json
{
  "recommendations": [
    "dart-code.flutter",
    "dart-code.dart-code",
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "usernamehw.errorlens",
    "esbenp.prettier-vscode"
  ]
}
```

**VS Code Settings**
```json
{
  "dart.flutterSdkPath": "/path/to/flutter",
  "dart.closingLabels": true,
  "dart.openDevTools": "flutter",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": true,
    "source.organizeImports": true
  },
  "[dart]": {
    "editor.defaultFormatter": "Dart-Code.dart-code",
    "editor.formatOnSave": true,
    "editor.tabSize": 2
  }
}
```

**Launch Configuration**
Create `.vscode/launch.json`:
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug (Development)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main.dart",
      "args": ["--flavor", "development", "--dart-define-from-file=.env.development"]
    },
    {
      "name": "Debug (Production)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main.dart",
      "args": ["--flavor", "production", "--dart-define-from-file=.env.production"]
    }
  ]
}
```

### Android Studio Setup

**Required Plugins**
- Flutter plugin
- Dart plugin
- Firebase plugin
- Git Integration

**Android Emulator Setup**
```bash
# Create Android Virtual Device (AVD)
# Recommended: Pixel 6 Pro with API 33 (Android 13)

# Start emulator
flutter emulators
flutter emulators --launch <emulator_id>
```

### iOS Simulator Setup (macOS only)

```bash
# List available simulators
xcrun simctl list devices

# Boot iPhone simulator
open -a Simulator

# Run on iOS
flutter run -d ios
```

## 📱 Device Testing Setup

### Android Device Configuration

**Enable Developer Options**
1. Settings → About phone → Tap "Build number" 7 times
2. Settings → Developer options → Enable "USB debugging"
3. Connect device via USB and authorize computer

**Verify Device Connection**
```bash
# List connected devices
flutter devices

# Install and run on device
flutter run -d <device_id>
```

### iOS Device Configuration (macOS only)

**Xcode Configuration**
1. Open `ios/Runner.xcworkspace` in Xcode
2. Configure development team and provisioning profile
3. Set minimum deployment target to iOS 12.0

**Device Installation**
```bash
# Install on iOS device
flutter install --device-id <ios_device_id>
```

## 🧪 Testing Environment

### Unit and Widget Testing

```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/services/auth_service_test.dart

# Run tests with coverage
flutter test --coverage

# Generate coverage report
genhtml coverage/lcov.info -o coverage/html
```

### Integration Testing

```bash
# Run integration tests
flutter test integration_test/

# Run on specific device
flutter test integration_test/ -d <device_id>
```

### Performance Testing

```bash
# Run performance tests
flutter drive --target=test_driver/perf_test.dart

# Profile build performance
flutter build apk --analyze-size
```

## 🔥 Firebase Development

### Local Firebase Emulators

```bash
# Install Firebase emulators
firebase setup:emulators:firestore
firebase setup:emulators:auth
firebase setup:emulators:storage

# Start emulators
firebase emulators:start

# Run app with emulators
flutter run --dart-define=USE_FIREBASE_EMULATOR=true
```

**Emulator UI Access**
- **Firestore**: http://localhost:4000/firestore
- **Auth**: http://localhost:4000/auth
- **Storage**: http://localhost:4000/storage

### Firestore Security Rules Testing

```bash
# Test security rules
firebase emulators:exec --only firestore "npm test"

# Deploy rules to development
firebase deploy --only firestore:rules --project journeyman-jobs-dev
```

## 🚢 Build and Deployment

### Android Build

**Debug Build**
```bash
# Build debug APK
flutter build apk --debug --flavor development

# Install debug build
flutter install --debug
```

**Release Build**
```bash
# Build release APK
flutter build apk --release --flavor production

# Build App Bundle for Play Store
flutter build appbundle --release --flavor production
```

### iOS Build (macOS only)

**Debug Build**
```bash
# Build for iOS simulator
flutter build ios --debug --simulator

# Build for iOS device
flutter build ios --debug
```

**Release Build**
```bash
# Build for App Store
flutter build ipa --release --flavor production

# Archive for distribution
xcodebuild -workspace ios/Runner.xcworkspace \
  -scheme Runner \
  -archivePath build/ios/archive/Runner.xcarchive \
  archive
```

## 🎯 Development Workflow

### Daily Development Process

1. **Start Development Server**
   ```bash
   # Start Firebase emulators
   firebase emulators:start
   
   # In new terminal, start Flutter
   flutter run --flavor development
   ```

2. **Code Changes**
   - Use hot reload (press `r` in terminal or Ctrl+S in IDE)
   - Use hot restart (press `R` in terminal) for major changes

3. **Testing**
   ```bash
   # Run affected tests
   flutter test test/path/to/modified_test.dart
   
   # Run all tests before commit
   flutter test
   ```

4. **Code Quality**
   ```bash
   # Format code
   dart format .
   
   # Analyze code
   flutter analyze
   
   # Check for unused dependencies
   dart pub deps
   ```

### Git Workflow

```bash
# Create feature branch
git checkout -b feature/electrical-calculator-enhancement

# Stage and commit changes
git add .
git commit -m "feat(calculator): add voltage drop calculation for IBEW standards"

# Push feature branch
git push origin feature/electrical-calculator-enhancement

# Create pull request via GitHub/GitLab
```

**Commit Message Format**
```
type(scope): description

feat(jobs): add storm work priority filtering
fix(auth): resolve Google Sign-In on Android devices
docs(electrical): update transformer training documentation
test(calculator): add voltage drop calculation tests
```

## 🔍 Debugging and Troubleshooting

### Common Issues and Solutions

**Flutter Doctor Issues**
```bash
# Fix Android license issues
flutter doctor --android-licenses

# Update Flutter
flutter upgrade

# Clear Flutter cache
flutter clean
flutter pub get
```

**Firebase Connection Issues**
```bash
# Verify Firebase configuration
firebase projects:list

# Re-initialize Firebase
firebase logout
firebase login
firebase use --add
```

**Build Issues**
```bash
# Clean build cache
flutter clean
cd ios && rm -rf Pods Podfile.lock && pod install
cd android && ./gradlew clean

# Reset Flutter
flutter pub cache repair
```

### Performance Debugging

**Flutter Inspector**
```bash
# Open Flutter Inspector
flutter inspector

# Debug painting (in app)
import 'package:flutter/rendering.dart';
debugPaintSizeEnabled = true;
```

**Performance Profiling**
```bash
# Profile performance
flutter run --profile

# Trace performance
flutter drive --target=test_driver/perf_test.dart --trace-startup
```

## 📚 Additional Resources

### Documentation Links
- [Flutter Documentation](https://flutter.dev/docs)
- [Firebase Flutter Documentation](https://firebase.flutter.dev/)
- [Dart Language Tour](https://dart.dev/guides/language/language-tour)
- [IBEW Official Website](https://www.ibew.org/)

### Development Tools
- **Flutter DevTools**: Performance and debugging tools
- **Firebase Console**: Backend management interface
- **Postman**: API testing (for NOAA weather services)
- **Charles Proxy**: Network debugging tool

### Team Resources
- **Slack**: #journeyman-jobs-dev channel
- **GitHub**: Repository and issue tracking
- **Firebase Console**: Shared development project
- **Design System**: Figma electrical component library

---

*This setup guide ensures all developers can contribute effectively to the electrical worker-focused Journeyman Jobs application.*