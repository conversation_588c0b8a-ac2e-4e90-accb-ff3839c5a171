# API Documentation

*Comprehensive API reference for services, data models, and external integrations*

## 🔥 Firebase Services

### Authentication Service

The `AuthService` provides secure authentication methods tailored for IBEW electrical workers.

#### Class: `AuthService`

**Constructor**
```dart
AuthService()
```

**Properties**
```dart
User? get currentUser               // Current Firebase user
Stream<User?> get authStateChanges  // Auth state change stream
```

**Methods**

##### Email & Password Authentication
```dart
Future<UserCredential?> signUpWithEmailAndPassword({
  required String email,
  required String password,
})
```
- **Purpose**: Register new IBEW member with email/password
- **Parameters**:
  - `email`: IBEW member email address
  - `password`: Secure password (minimum 8 characters)
- **Returns**: `UserCredential` on success, `null` on failure
- **Throws**: Formatted error messages for common Firebase auth errors

```dart
Future<UserCredential?> signInWithEmailAndPassword({
  required String email,
  required String password,
})
```
- **Purpose**: Sign in existing IBEW member
- **Parameters**: Same as sign up
- **Returns**: `UserCredential` on success
- **Error Handling**: User-friendly messages for electrical workers

##### Social Authentication
```dart
Future<UserCredential?> signInWithGoogle()
```
- **Purpose**: Google Sign-In for quick access
- **Features**: 
  - Automatic Google Sign-In initialization
  - Platform compatibility checking
  - v7 API integration with scope handling
- **Returns**: `UserCredential` with Google account info

```dart
Future<UserCredential?> signInWithApple()
```
- **Purpose**: Apple Sign-In for iOS users
- **Features**:
  - Availability checking
  - Full name and email scope requests
  - OAuth credential creation
- **Platform**: iOS/macOS only

##### Account Management
```dart
Future<void> sendPasswordResetEmail({required String email})
```
- **Purpose**: Password recovery for electrical workers
- **Parameters**: `email` - Account email address

```dart
Future<void> signOut()
```
- **Purpose**: Secure sign out from all providers
- **Features**: Clears Google Sign-In and Firebase sessions

```dart
Future<void> deleteAccount()
```
- **Purpose**: Permanently delete IBEW member account
- **Security**: Requires recent authentication

##### Error Handling
```dart
String _handleAuthException(FirebaseAuthException e)
```
**Supported Error Codes**:
- `weak-password`: Password strength requirements
- `email-already-in-use`: Duplicate account prevention
- `invalid-email`: Email format validation
- `user-disabled`: Account suspension
- `user-not-found`: Account lookup failure
- `wrong-password`: Authentication failure
- `too-many-requests`: Rate limiting protection

---

### Firestore Service

#### Class: `ResilientFirestoreService`

Provides robust Firestore operations with retry logic and error handling.

**Constructor**
```dart
ResilientFirestoreService()
```

**Core Methods**

##### Document Operations
```dart
Future<DocumentSnapshot<Map<String, dynamic>>> getDocument(
  String collection,
  String documentId, {
  int maxRetries = 3,
})
```
- **Purpose**: Retrieve single document with retry logic
- **Parameters**:
  - `collection`: Firestore collection name
  - `documentId`: Document ID
  - `maxRetries`: Retry attempts for network failures
- **Returns**: Document snapshot with data

```dart
Future<void> setDocument(
  String collection,
  String documentId,
  Map<String, dynamic> data, {
  bool merge = false,
})
```
- **Purpose**: Create or update document
- **Parameters**:
  - `data`: Document data map
  - `merge`: Merge with existing data if true

##### Collection Queries
```dart
Future<QuerySnapshot<Map<String, dynamic>>> getCollection(
  String collection, {
  Query<Map<String, dynamic>>? Function(CollectionReference<Map<String, dynamic>>)? queryBuilder,
  int maxRetries = 3,
})
```
- **Purpose**: Query collection with optional filters
- **Parameters**:
  - `queryBuilder`: Optional query modification function
- **Example**:
```dart
final jobs = await firestoreService.getCollection(
  'jobs',
  queryBuilder: (ref) => ref
    .where('local', isEqualTo: 123)
    .where('wage', isGreaterThan: 35.0)
    .orderBy('timestamp', descending: true)
    .limit(50),
);
```

##### Batch Operations
```dart
Future<void> batchWrite(List<BatchOperation> operations)
```
- **Purpose**: Atomic multi-document operations
- **Parameters**: List of `BatchOperation` objects
- **Example**:
```dart
await firestoreService.batchWrite([
  BatchOperation.set('users', userId, userData),
  BatchOperation.update('jobs', jobId, {'applied': true}),
  BatchOperation.delete('temp', tempId),
]);
```

---

## 📋 Data Models

### Job Model

#### Class: `Job`

Represents IBEW job postings with comprehensive electrical industry fields.

**Constructor**
```dart
const Job({
  required this.id,
  this.reference,
  this.local,
  this.classification,
  required this.company,
  required this.location,
  // ... other parameters
})
```

**Core Properties**

##### Basic Information
```dart
final String id;                    // Unique job identifier
final String company;               // Contractor/employer name
final String location;              // Job location (city, state)
final String? jobTitle;             // Job title or description
final String? jobDescription;       // Detailed job description
```

##### IBEW-Specific Fields
```dart
final int? local;                   // IBEW local union number
final String? classification;       // Worker classification (Inside Wireman, etc.)
final int? localNumber;            // Local number (alternative field)
final String? jobClass;            // Job classification details
final List<int>? booksYourOn;      // Book numbers for referral
```

##### Compensation
```dart
final double? wage;                 // Hourly wage rate
final String? perDiem;             // Per diem allowance details
final String? agreement;           // Union agreement/contract
```

##### Schedule and Duration
```dart
final int? hours;                  // Work hours (daily/weekly)
final String? startDate;          // Job start date
final String? startTime;          // Daily start time
final String? duration;           // Job duration/length
final DateTime? timestamp;         // Posted timestamp
```

##### Work Details
```dart
final String? qualifications;      // Required certifications/skills
final String? typeOfWork;         // Work type (commercial, industrial, storm)
final String? numberOfJobs;       // Number of positions available
final String? voltageLevel;       // Voltage classification
```

**Methods**

##### JSON Serialization
```dart
factory Job.fromJson(Map<String, dynamic> json)
```
- **Purpose**: Create Job instance from JSON data
- **Features**:
  - Handles multiple JSON formats from different union job boards
  - Intelligent field mapping and data type conversion
  - Robust error handling for malformed data
- **Field Mapping Examples**:
```dart
// Handles various field name variations
final wage = parseDouble(json['wage']) ?? 
             parseDouble(json['hourlyWage']) ??
             parseDouble(json['Hourly Rate']);

final location = json['location']?.toString() ?? 
                json['Location']?.toString() ?? 
                json['job_location']?.toString();
```

```dart
Map<String, dynamic> toJson({
  bool useFirestoreTypes = false,
  bool includeNullValues = false,
})
```
- **Purpose**: Convert Job to JSON for storage/transmission
- **Parameters**:
  - `useFirestoreTypes`: Convert DateTime to Firestore Timestamp
  - `includeNullValues`: Include null fields in output

##### Firestore Integration
```dart
factory Job.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc)
Map<String, dynamic> toFirestore()
```
- **Purpose**: Seamless Firestore document conversion
- **Features**: Automatic document ID inclusion and type conversion

##### Utility Methods
```dart
Job copyWith({/* all properties optional */})
```
- **Purpose**: Create modified copy of Job instance
- **Usage**: State updates and data manipulation

**Validation and Parsing**

The Job model includes sophisticated parsing for union job board data:

```dart
// Helper functions for data validation
int? parseInt(dynamic value) {
  if (value == null) return null;
  if (value is int) return value;
  if (value is String) return int.tryParse(value);
  if (value is double) return value.toInt();
  return null;
}

double? parseDouble(dynamic value) {
  if (value == null) return null;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) {
    // Handle currency formatting
    String cleanValue = value
        .replaceAll(RegExp(r'[\$,]'), '')
        .replaceAll('/hr', '')
        .replaceAll('/hour', '')
        .trim();
    return double.tryParse(cleanValue);
  }
  return null;
}
```

---

### User Model

#### Class: `UserModel`

Represents IBEW member profiles with comprehensive electrical worker information.

**Constructor**
```dart
const UserModel({
  required this.uid,
  required this.email,
  required this.firstName,
  required this.lastName,
  // ... other parameters
})
```

**Properties**

##### Personal Information
```dart
final String uid;                   // Firebase user ID
final String email;                 // Email address
final String firstName;             // First name
final String lastName;              // Last name
final String? phoneNumber;          // Contact phone
final String? address1;             // Primary address
final String? address2;             // Secondary address line
final String? city;                 // City
final String? state;                // State/province
final int? zipcode;                 // Postal code
```

##### IBEW Professional Details
```dart
final int? homeLocal;               // Home IBEW local union number
final int? ticketNumber;            // IBEW ticket/member number
final Classification? classification; // Worker classification enum
final List<String>? constructionTypes; // Preferred work types
final bool? isWorking;              // Current employment status
```

##### Job Preferences
```dart
final double? minHourlyRate;        // Minimum acceptable wage
final double? maxHourlyRate;        // Maximum wage expectation
final String? preferredLocal1;      // Primary preferred local
final String? preferredLocal2;      // Secondary preferred local
final String? preferredLocal3;      // Tertiary preferred local
final bool? networkWithOthers;      // Open to networking
final bool? careerAdvancements;     // Seeking advancement
final bool? betterBenefits;         // Benefits priority
```

##### App Configuration
```dart
final bool? aiWidgetEnabled;        // AI recommendations enabled
final String? onboardingStatus;     // Onboarding completion state
final DateTime? createdTime;        // Account creation timestamp
```

**Enums**

##### Classification
```dart
enum Classification {
  insideWireman,
  journeymanLineman,
  treeTrimmer,
  equipmentOperator,
  groundman,
  apprentice,
  foreman,
  generalForeman,
}
```

**Usage Examples**

```dart
// Create new user profile
final newUser = UserModel(
  uid: 'user123',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  homeLocal: 123,
  ticketNumber: 45678,
  classification: Classification.insideWireman,
  constructionTypes: ['Commercial', 'Industrial'],
  minHourlyRate: 35.0,
  maxHourlyRate: 55.0,
);

// Update user preferences
final updatedUser = currentUser.copyWith(
  minHourlyRate: 40.0,
  preferredLocal1: 'Local 456',
  aiWidgetEnabled: true,
);
```

---

### Filter Criteria Model

#### Class: `FilterCriteria`

Defines job search and filtering parameters for electrical workers.

**Constructor**
```dart
const FilterCriteria({
  this.location,
  this.radius,
  this.states,
  this.minWage,
  this.maxWage,
  // ... other parameters
})
```

**Properties**

##### Location Filters
```dart
final String? location;             // Search location
final double? radius;               // Search radius in miles
final List<String>? states;         // State restrictions
final List<int>? locals;           // Specific IBEW locals
```

##### Compensation Filters
```dart
final double? minWage;              // Minimum hourly wage
final double? maxWage;              // Maximum hourly wage
final bool? perDiemRequired;        // Requires per diem
final List<String>? agreements;     // Union agreements
```

##### Job Type Filters
```dart
final List<String>? constructionTypes; // Work types
final List<String>? classifications;   // Worker classifications
final List<String>? voltagelevels;     // Voltage categories
final bool? stormWorkOnly;             // Emergency work only
```

##### Timing Filters
```dart
final DateTime? startAfter;         // Earliest start date
final DateTime? startBefore;        // Latest start date
final List<String>? duration;       // Job duration preferences
final int? maxPositions;            // Position count filter
```

**Methods**

```dart
FilterCriteria copyWith({/* all properties optional */})
bool get isEmpty                    // Check if no filters applied
bool get hasLocationFilter          // Check location filters
bool get hasWageFilter             // Check wage filters
```

**Usage Examples**

```dart
// Create comprehensive job filter
final filter = FilterCriteria(
  location: 'Chicago, IL',
  radius: 100.0,
  minWage: 40.0,
  constructionTypes: ['Commercial', 'Industrial'],
  classifications: ['Inside Wireman'],
  stormWorkOnly: false,
  startAfter: DateTime.now(),
  perDiemRequired: true,
);

// Apply filters to job query
final filteredJobs = await jobService.searchJobs(filter);
```

---

## 🌐 External API Integration

### NOAA Weather Service

#### Class: `NOAAWeatherService`

Provides real-time weather data for electrical worker safety and storm work opportunities.

**Constructor**
```dart
NOAAWeatherService({
  required this.httpClient,
  this.baseUrl = 'https://api.weather.gov',
})
```

**Methods**

##### Weather Alerts
```dart
Future<List<WeatherAlert>> getActiveAlerts({
  String? state,
  String? zone,
  AlertSeverity? minSeverity,
})
```
- **Purpose**: Retrieve active weather alerts for electrical workers
- **Parameters**:
  - `state`: State code (e.g., 'IL', 'TX')
  - `zone`: Weather zone identifier
  - `minSeverity`: Minimum alert severity level
- **Returns**: List of relevant weather alerts

##### Radar Data
```dart
Future<RadarData> getRadarData({
  required double latitude,
  required double longitude,
  required RadarProduct product,
})
```
- **Purpose**: Real-time weather radar for storm tracking
- **Parameters**:
  - `latitude/longitude`: Location coordinates
  - `product`: Radar product type (precipitation, velocity, etc.)
- **Returns**: Radar imagery and metadata

##### Storm Prediction
```dart
Future<List<StormOutlook>> getStormOutlook({
  required DateTime date,
  required OutlookType type,
})
```
- **Purpose**: Storm prediction for emergency response planning
- **Parameters**:
  - `date`: Forecast date
  - `type`: Outlook type (convective, winter, etc.)

**Data Models**

##### WeatherAlert
```dart
class WeatherAlert {
  final String id;
  final String event;                 // Alert type (Severe Thunderstorm, etc.)
  final AlertSeverity severity;       // Minor, Moderate, Severe, Extreme
  final String headline;              // Brief description
  final String description;           // Detailed alert text
  final DateTime onset;               // Alert start time
  final DateTime expires;             // Alert expiration
  final List<String> affectedAreas;   // Geographic areas
  final bool affectsElectricalWork;   // Relevant to electrical workers
}
```

##### AlertSeverity
```dart
enum AlertSeverity {
  minor,      // Minimal threat to electrical work
  moderate,   // Some threat, precautions advised
  severe,     // High threat, work restrictions
  extreme,    // Extreme threat, work suspension
}
```

---

### Location Service

#### Class: `LocationService`

Handles GPS and location-based features for job searching and weather alerts.

**Constructor**
```dart
LocationService()
```

**Methods**

##### Location Access
```dart
Future<LocationPermissionStatus> requestLocationPermission()
```
- **Purpose**: Request location permissions with electrical worker context
- **Returns**: Permission status

```dart
Future<Position?> getCurrentLocation({
  LocationAccuracy accuracy = LocationAccuracy.balanced,
})
```
- **Purpose**: Get current GPS coordinates
- **Parameters**: `accuracy` - GPS accuracy level
- **Returns**: Position with coordinates

##### Location Utilities
```dart
Future<List<Placemark>> getPlacemarkFromCoordinates(
  double latitude,
  double longitude,
)
```
- **Purpose**: Reverse geocoding for location names
- **Returns**: Address information

```dart
double calculateDistance(
  double startLatitude,
  double startLongitude,
  double endLatitude,
  double endLongitude,
)
```
- **Purpose**: Calculate distance between locations
- **Returns**: Distance in meters
- **Usage**: Job proximity calculations

---

## 🔧 Service Integration Patterns

### Service Composition

```dart
class JobSearchService {
  final ResilientFirestoreService _firestoreService;
  final LocationService _locationService;
  final NOAAWeatherService _weatherService;
  
  JobSearchService({
    required ResilientFirestoreService firestoreService,
    required LocationService locationService,
    required NOAAWeatherService weatherService,
  }) : _firestoreService = firestoreService,
       _locationService = locationService,
       _weatherService = weatherService;
  
  Future<List<Job>> searchJobsWithWeatherContext(
    FilterCriteria criteria,
  ) async {
    // Get base job results
    final jobs = await _searchJobs(criteria);
    
    // Add weather context for storm work
    if (criteria.stormWorkOnly == true) {
      final location = await _locationService.getCurrentLocation();
      if (location != null) {
        final alerts = await _weatherService.getActiveAlerts(
          state: await _getStateFromLocation(location),
          minSeverity: AlertSeverity.severe,
        );
        
        // Filter jobs based on weather alerts
        return _filterJobsByWeatherAlerts(jobs, alerts);
      }
    }
    
    return jobs;
  }
}
```

### Error Handling Patterns

```dart
class ServiceErrorHandler {
  static Future<T> withRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
  }) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (e) {
        if (attempt == maxRetries) {
          throw ServiceException('Operation failed after $maxRetries attempts: $e');
        }
        
        await Future.delayed(delay * attempt); // Exponential backoff
      }
    }
    
    throw ServiceException('Unexpected error in retry logic');
  }
  
  static bool isRetryableError(dynamic error) {
    return error is SocketException ||
           error is TimeoutException ||
           (error is FirebaseException && error.code == 'unavailable');
  }
}
```

---

*This API documentation covers all major services and data models used throughout the Journeyman Jobs application, with specific focus on electrical industry requirements and mobile field worker use cases.*