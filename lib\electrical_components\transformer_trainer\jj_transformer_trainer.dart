import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../design_system/app_theme.dart';
import 'models/transformer_models.dart';
import 'state/transformer_state.dart';
import 'modes/guided_mode.dart';
import 'modes/quiz_mode.dart';

/// Journeyman Jobs themed transformer trainer widget
class JJTransformerTrainer extends StatelessWidget {
  final TransformerBankType initialBankType;
  final TrainingMode initialMode;
  final DifficultyLevel initialDifficulty;
  final Function(TrainingStep)? onStepComplete;
  final Function(TransformerBankType)? onBankComplete;
  final Function(String)? onError;

  const JJTransformerTrainer({
    Key? key,
    this.initialBankType = TransformerBankType.wyeToWye,
    this.initialMode = TrainingMode.guided,
    this.initialDifficulty = DifficultyLevel.beginner,
    this.onStepComplete,
    this.onBankComplete,
    this.onError,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => TransformerTrainerState()
        ..updateState(TrainingState(
          bankType: initialBankType,
          mode: initialMode,
          difficulty: initialDifficulty,
        )),
      child: Consumer<TransformerTrainerState>(
        builder: (context, state, child) {
          return Column(
            children: [
              _buildControlPanel(context, state),
              Expanded(
                child: _buildMainContent(context, state),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildControlPanel(BuildContext context, TransformerTrainerState state) {
    return Container(
      color: AppTheme.white,
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Mode Toggle
          Text(
            'Training Mode',
            style: AppTheme.labelMedium.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSm),
          _buildModeToggle(context, state),
          
          const SizedBox(height: AppTheme.spacingMd),
          
          // Bank Type Selector
          Text(
            'Transformer Configuration',
            style: AppTheme.labelMedium.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSm),
          _buildBankTypeSelector(context, state),
          
          const SizedBox(height: AppTheme.spacingMd),
          
          // Difficulty Selector
          Text(
            'Difficulty Level',
            style: AppTheme.labelMedium.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSm),
          _buildDifficultySelector(context, state),
        ],
      ),
    );
  }

  Widget _buildModeToggle(BuildContext context, TransformerTrainerState state) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.lightGray,
        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => state.setMode(TrainingMode.guided),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: AppTheme.spacingSm,
                  horizontal: AppTheme.spacingMd,
                ),
                decoration: BoxDecoration(
                  color: state.currentState.mode == TrainingMode.guided
                      ? AppTheme.primaryNavy
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.school,
                      size: AppTheme.iconSm,
                      color: state.currentState.mode == TrainingMode.guided
                          ? AppTheme.white
                          : AppTheme.textSecondary,
                    ),
                    const SizedBox(width: AppTheme.spacingSm),
                    Text(
                      'Guided',
                      style: AppTheme.labelMedium.copyWith(
                        color: state.currentState.mode == TrainingMode.guided
                            ? AppTheme.white
                            : AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => state.setMode(TrainingMode.quiz),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: AppTheme.spacingSm,
                  horizontal: AppTheme.spacingMd,
                ),
                decoration: BoxDecoration(
                  color: state.currentState.mode == TrainingMode.quiz
                      ? AppTheme.primaryNavy
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.quiz,
                      size: AppTheme.iconSm,
                      color: state.currentState.mode == TrainingMode.quiz
                          ? AppTheme.white
                          : AppTheme.textSecondary,
                    ),
                    const SizedBox(width: AppTheme.spacingSm),
                    Text(
                      'Quiz',
                      style: AppTheme.labelMedium.copyWith(
                        color: state.currentState.mode == TrainingMode.quiz
                            ? AppTheme.white
                            : AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBankTypeSelector(BuildContext context, TransformerTrainerState state) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: TransformerBankType.values.map((type) {
          final isSelected = state.currentState.bankType == type;
          return Container(
            margin: const EdgeInsets.only(right: AppTheme.spacingSm),
            child: GestureDetector(
              onTap: () => state.setBankType(type),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: AppTheme.spacingSm,
                  horizontal: AppTheme.spacingMd,
                ),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppTheme.accentCopper.withValues(alpha: 0.1)
                      : AppTheme.lightGray,
                  border: Border.all(
                    color: isSelected ? AppTheme.accentCopper : AppTheme.mediumGray,
                    width: isSelected ? 2 : 1,
                  ),
                  borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                ),
                child: Text(
                  _getBankTypeDisplayName(type),
                  style: AppTheme.labelSmall.copyWith(
                    color: isSelected ? AppTheme.accentCopper : AppTheme.textSecondary,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildDifficultySelector(BuildContext context, TransformerTrainerState state) {
    return Row(
      children: DifficultyLevel.values.map((level) {
        final isSelected = state.currentState.difficulty == level;
        return Expanded(
          child: Container(
            margin: const EdgeInsets.only(right: AppTheme.spacingSm),
            child: GestureDetector(
              onTap: () => state.setDifficulty(level),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingSm),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppTheme.accentCopper.withValues(alpha: 0.1)
                      : AppTheme.lightGray,
                  border: Border.all(
                    color: isSelected ? AppTheme.accentCopper : AppTheme.mediumGray,
                    width: isSelected ? 2 : 1,
                  ),
                  borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                ),
                child: Text(
                  _getDifficultyDisplayName(level),
                  textAlign: TextAlign.center,
                  style: AppTheme.labelSmall.copyWith(
                    color: isSelected ? AppTheme.accentCopper : AppTheme.textSecondary,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildMainContent(BuildContext context, TransformerTrainerState state) {
    return Container(
      color: AppTheme.lightGray,
      child: state.currentState.mode == TrainingMode.guided
          ? GuidedModeWidget(
              onStepComplete: onStepComplete,
              onBankComplete: onBankComplete,
              onError: onError,
            )
          : QuizModeWidget(
              onStepComplete: onStepComplete,
              onBankComplete: onBankComplete,
              onError: onError,
            ),
    );
  }

  String _getBankTypeDisplayName(TransformerBankType type) {
    switch (type) {
      case TransformerBankType.wyeToWye:
        return 'Wye-Wye';
      case TransformerBankType.wyeToDelta:
        return 'Wye-Delta';
      case TransformerBankType.deltaToWye:
        return 'Delta-Wye';
      case TransformerBankType.deltaToDelta:
        return 'Delta-Delta';
      case TransformerBankType.openDelta:
        return 'Open-Delta';
    }
  }

  String _getDifficultyDisplayName(DifficultyLevel level) {
    switch (level) {
      case DifficultyLevel.beginner:
        return 'Beginner';
      case DifficultyLevel.intermediate:
        return 'Intermediate';
      case DifficultyLevel.advanced:
        return 'Advanced';
    }
  }
}