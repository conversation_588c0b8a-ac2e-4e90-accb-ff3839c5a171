# Popup Theme Specification

## Standardized Design Pattern for Journeyman Jobs App

### Executive Summary

This specification formalizes the design pattern extracted from the LocalCard component to create consistent theming across all 51 popup implementations in the application. Based on comprehensive research, this document provides a unified approach to popup styling, ensuring brand consistency and improved user experience.

---

## 1. Core Design Principles

### 1.1 Consistency First

- **All popup components must use AppTheme constants**
- No hardcoded values for colors, spacing, or dimensions
- Unified visual language across all popup types

### 1.2 Visual Hierarchy

- Clear elevation patterns to indicate component importance
- Consistent use of borders and shadows for depth
- Predictable spacing for content organization

### 1.3 Brand Identity

- Copper accent color (`AppTheme.accentCopper`) for interactive elements
- Navy primary color (`AppTheme.primaryNavy`) for headers and emphasis
- White backgrounds for clarity and readability

---

## 2. Standard Elevation Values

| Popup Type | Elevation | Shadow | Use Case |
|------------|-----------|---------|----------|
| **AlertDialog** | 4 | `AppTheme.shadowMd` | Critical user decisions, confirmations |
| **BottomSheet** | 8 | `AppTheme.shadowLg` | Content selection, forms, filters |
| **Custom Popup** | 2 | `AppTheme.shadowSm` | Tooltips, contextual information |
| **SnackBar** | 1 | `AppTheme.shadowSm` | Transient messages, notifications |
| **Modal Overlay** | 6 | `AppTheme.shadowMd` | Full-screen modals, image viewers |

---

## 3. Border Styling Guidelines

### 3.1 Border Radius Standards

```dart
// Primary radius for all popups
static const double popupRadius = AppTheme.radiusLg; // 16.0

// Specific overrides
static const Map<PopupType, double> borderRadiusMap = {
  PopupType.alertDialog: AppTheme.radiusLg,      // 16.0
  PopupType.bottomSheet: AppTheme.radiusXl,      // 20.0 (top only)
  PopupType.customPopup: AppTheme.radiusLg,      // 16.0
  PopupType.snackBar: AppTheme.radiusMd,         // 12.0
  PopupType.contextMenu: AppTheme.radiusSm,      // 8.0
};
```

### 3.2 Border Color & Width

```dart
// Standard border configuration
static const BorderSide standardPopupBorder = BorderSide(
  color: AppTheme.accentCopper,
  width: AppTheme.borderWidthThin, // 1.0
);

// Focus state border
static const BorderSide focusPopupBorder = BorderSide(
  color: AppTheme.accentCopper,
  width: AppTheme.borderWidthMedium, // 1.5
);

// Secondary border (for less prominent popups)
static const BorderSide secondaryPopupBorder = BorderSide(
  color: AppTheme.borderLight,
  width: AppTheme.borderWidthThin, // 1.0
);
```

---

## 4. Background Color Standards

### 4.1 Primary Backgrounds

```dart
static const Map<PopupType, Color> backgroundColors = {
  PopupType.alertDialog: AppTheme.white,
  PopupType.bottomSheet: AppTheme.white,
  PopupType.customPopup: AppTheme.white,
  PopupType.snackBar: AppTheme.primaryNavy,
  PopupType.contextMenu: AppTheme.white,
};
```

### 4.2 Overlay Colors

```dart
// Modal barrier color
static final Color barrierColor = AppTheme.black.withOpacity(0.5);

// Transparent overlays for full-screen popups
static final Color transparentOverlay = Colors.transparent;
```

---

## 5. Spacing and Padding Guidelines

### 5.1 Container Padding

```dart
static const Map<PopupType, EdgeInsets> paddingMap = {
  PopupType.alertDialog: EdgeInsets.all(AppTheme.spacingLg),        // 24.0
  PopupType.bottomSheet: EdgeInsets.fromLTRB(
    AppTheme.spacingLg,    // left: 24.0
    AppTheme.spacingXl,    // top: 32.0 (includes drag handle)
    AppTheme.spacingLg,    // right: 24.0
    AppTheme.spacingLg,    // bottom: 24.0
  ),
  PopupType.customPopup: EdgeInsets.all(AppTheme.spacingMd),        // 16.0
  PopupType.snackBar: EdgeInsets.symmetric(
    horizontal: AppTheme.spacingMd,  // 16.0
    vertical: AppTheme.spacingSm,    // 8.0
  ),
};
```

### 5.2 Content Spacing

```dart
// Vertical spacing between popup elements
static const double itemSpacing = AppTheme.spacingMd;        // 16.0
static const double sectionSpacing = AppTheme.spacingLg;     // 24.0

// Button spacing
static const double buttonSpacing = AppTheme.spacingSm;      // 8.0
```

---

## 6. Color Usage Rules

### 6.1 Text Colors

| Element | Color | Usage |
|---------|-------|-------|
| **Title** | `AppTheme.textPrimary` | Dialog titles, headers |
| **Body** | `AppTheme.textSecondary` | Descriptions, content |
| **Action** | `AppTheme.accentCopper` | Buttons, links |
| **Disabled** | `AppTheme.textLight` | Inactive elements |
| **Error** | `AppTheme.errorRed` | Error messages |

### 6.2 Interactive Elements

```dart
// Primary action button
static const Color primaryActionColor = AppTheme.accentCopper;

// Secondary action button
static const Color secondaryActionColor = AppTheme.primaryNavy;

// Destructive action
static const Color destructiveActionColor = AppTheme.errorRed;

// Selection indicator
static const Color selectionColor = AppTheme.accentCopper.withOpacity(0.1);
```

---

## 7. Theme Variations

### 7.1 AlertDialog Theme

```dart
class AlertDialogTheme {
  static const BoxDecoration decoration = BoxDecoration(
    color: AppTheme.white,
    borderRadius: BorderRadius.all(
      Radius.circular(AppTheme.radiusLg),
    ),
    border: Border.fromBorderSide(standardPopupBorder),
  );
  
  static const EdgeInsets padding = EdgeInsets.all(AppTheme.spacingLg);
  static const double elevation = 4;
  
  // Title styling
  static final TextStyle titleStyle = AppTheme.headlineSmall;
  
  // Content styling
  static final TextStyle contentStyle = AppTheme.bodyMedium.copyWith(
    color: AppTheme.textSecondary,
  );
  
  // Action button styling
  static final ButtonStyle primaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: AppTheme.accentCopper,
    foregroundColor: AppTheme.white,
  );
}
```

### 7.2 BottomSheet Theme

```dart
class BottomSheetTheme {
  static const BoxDecoration decoration = BoxDecoration(
    color: AppTheme.white,
    borderRadius: BorderRadius.vertical(
      top: Radius.circular(AppTheme.radiusXl),
    ),
  );
  
  static const EdgeInsets padding = EdgeInsets.fromLTRB(
    AppTheme.spacingLg,
    AppTheme.spacingXl,
    AppTheme.spacingLg,
    AppTheme.spacingLg,
  );
  
  static const double elevation = 8;
  
  // Drag handle
  static const Widget dragHandle = Container(
    width: 40,
    height: 4,
    margin: EdgeInsets.only(top: AppTheme.spacingMd),
    decoration: BoxDecoration(
      color: AppTheme.lightGray,
      borderRadius: BorderRadius.circular(AppTheme.radiusXs),
    ),
  );
}
```

### 7.3 Custom Popup Theme

```dart
class CustomPopupTheme {
  static final BoxDecoration decoration = BoxDecoration(
    color: AppTheme.white,
    borderRadius: BorderRadius.circular(AppTheme.radiusLg),
    border: Border.fromBorderSide(standardPopupBorder),
  );
  
  static const EdgeInsets padding = EdgeInsets.all(AppTheme.spacingMd);
  static const double elevation = 2;
  
  // Matches LocalCard styling exactly
  static final ShapeBorder shape = RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(AppTheme.radiusLg),
    side: BorderSide(
      color: AppTheme.accentCopper,
      width: AppTheme.borderWidthThin,
    ),
  );
}
```

### 7.4 SnackBar Theme

```dart
class SnackBarTheme {
  static const BoxDecoration decoration = BoxDecoration(
    color: AppTheme.primaryNavy,
    borderRadius: BorderRadius.all(
      Radius.circular(AppTheme.radiusMd),
    ),
  );
  
  static const EdgeInsets padding = EdgeInsets.symmetric(
    horizontal: AppTheme.spacingMd,
    vertical: AppTheme.spacingSm,
  );
  
  static const double elevation = 1;
  
  // Text styling
  static final TextStyle messageStyle = AppTheme.bodyMedium.copyWith(
    color: AppTheme.textOnDark,
  );
  
  // Error variant
  static const BoxDecoration errorDecoration = BoxDecoration(
    color: AppTheme.errorRed,
    borderRadius: BorderRadius.all(
      Radius.circular(AppTheme.radiusMd),
    ),
  );
}
```

---

## 8. Implementation Approach

### 8.1 Create PopupTheme Widget

```dart
class PopupTheme extends InheritedWidget {
  final PopupThemeData data;
  
  const PopupTheme({
    Key? key,
    required this.data,
    required Widget child,
  }) : super(key: key, child: child);
  
  static PopupThemeData of(BuildContext context) {
    final PopupTheme? theme = 
        context.dependOnInheritedWidgetOfExactType<PopupTheme>();
    return theme?.data ?? PopupThemeData.standard();
  }
  
  @override
  bool updateShouldNotify(PopupTheme oldWidget) => data != oldWidget.data;
}
```

### 8.2 Factory Constructors

```dart
class PopupThemeData {
  // Factory constructor for each popup type
  factory PopupThemeData.alertDialog() => PopupThemeData(
    elevation: 4,
    borderRadius: AppTheme.radiusLg,
    borderColor: AppTheme.accentCopper,
    backgroundColor: AppTheme.white,
    padding: EdgeInsets.all(AppTheme.spacingLg),
  );
  
  factory PopupThemeData.bottomSheet() => PopupThemeData(
    elevation: 8,
    borderRadius: AppTheme.radiusXl,
    borderColor: Colors.transparent,
    backgroundColor: AppTheme.white,
    padding: EdgeInsets.fromLTRB(
      AppTheme.spacingLg,
      AppTheme.spacingXl,
      AppTheme.spacingLg,
      AppTheme.spacingLg,
    ),
  );
  
  factory PopupThemeData.customPopup() => PopupThemeData(
    elevation: 2,
    borderRadius: AppTheme.radiusLg,
    borderColor: AppTheme.accentCopper,
    backgroundColor: AppTheme.white,
    padding: EdgeInsets.all(AppTheme.spacingMd),
  );
  
  factory PopupThemeData.snackBar() => PopupThemeData(
    elevation: 1,
    borderRadius: AppTheme.radiusMd,
    borderColor: Colors.transparent,
    backgroundColor: AppTheme.primaryNavy,
    padding: EdgeInsets.symmetric(
      horizontal: AppTheme.spacingMd,
      vertical: AppTheme.spacingSm,
    ),
  );
}
```

### 8.3 Usage Example

```dart
// In your widget
showDialog(
  context: context,
  builder: (context) => PopupTheme(
    data: PopupThemeData.alertDialog(),
    child: CustomAlertDialog(
      title: 'Confirm Action',
      content: 'Are you sure you want to proceed?',
      onConfirm: () => Navigator.pop(context),
    ),
  ),
);
```

---

## 9. Edge Cases and Special Considerations

### 9.1 Full-Screen Dialogs

- Use `radiusXl` (20.0) for corners
- Transparent backdrop with 0.5 opacity
- Elevation of 6 for prominence
- Include close button in top-right corner

### 9.2 Date/Time Pickers

- Follow AlertDialog standards
- Maintain copper accent for selection indicators
- Use `spacingMd` between calendar elements

### 9.3 Context Menus

- Use `radiusSm` (8.0) for compact appearance
- Elevation of 1 for subtle shadow
- Minimal padding (`spacingSm`)
- Position relative to trigger element

### 9.4 Loading Overlays

- Semi-transparent background (0.7 opacity)
- Centered content with `radiusLg`
- No border, elevation of 2
- Include progress indicator with copper color

### 9.5 Toast Notifications

- Follow SnackBar theme
- Auto-dismiss after 3 seconds
- Support swipe-to-dismiss gesture
- Position at bottom with `spacingMd` margin

---

## 10. Migration Strategy

### Phase 1: Core Components (Week 1)

1. Implement PopupTheme widget
2. Create PopupThemeData class
3. Add factory constructors for each type

### Phase 2: Dialog Migration (Week 2)

1. Update all AlertDialog implementations
2. Standardize confirmation dialogs
3. Update error dialogs

### Phase 3: BottomSheet Migration (Week 3)

1. Update all BottomSheet implementations
2. Standardize selection sheets
3. Update filter sheets

### Phase 4: Custom Popups (Week 4)

1. Update all custom popup widgets
2. Standardize tooltips
3. Update context menus

### Phase 5: Testing & Documentation (Week 5)

1. Create widget tests for each popup type
2. Update component documentation
3. Create Storybook examples

---

## 11. Validation Checklist

Before marking a popup implementation as complete, verify:

- [ ] Uses AppTheme constants exclusively
- [ ] Follows elevation standards for its type
- [ ] Implements correct border radius
- [ ] Uses appropriate padding values
- [ ] Text colors follow usage rules
- [ ] Interactive elements use copper accent
- [ ] Background color matches specification
- [ ] Supports both light and dark themes
- [ ] Includes proper animations/transitions
- [ ] Accessibility features implemented

---

## 12. Code Quality Standards

### Linting Rules

Add these rules to `analysis_options.yaml`:

```yaml
linter:
  rules:
    - avoid_hardcoded_colors_in_popups
    - use_app_theme_constants
    - prefer_popup_theme_factory
```

### Code Review Checklist

- Verify no hardcoded values
- Check PopupTheme usage
- Validate color consistency
- Review spacing implementation
- Test on multiple screen sizes

---

## Appendix A: Quick Reference

### Popup Type Decision Tree

```
Is it a critical user decision?
  Yes → AlertDialog (elevation: 4)
  No → Continue
  
Is it a content selection?
  Yes → BottomSheet (elevation: 8)
  No → Continue
  
Is it a transient message?
  Yes → SnackBar (elevation: 1)
  No → Continue
  
Is it contextual information?
  Yes → Custom Popup (elevation: 2)
  No → Default to Custom Popup
```

### Color Quick Reference

- **Primary Action**: `AppTheme.accentCopper`
- **Secondary Action**: `AppTheme.primaryNavy`
- **Background**: `AppTheme.white`
- **Border**: `AppTheme.accentCopper` (1.0 width)
- **Text Primary**: `AppTheme.textPrimary`
- **Text Secondary**: `AppTheme.textSecondary`

### Spacing Quick Reference

- **Extra Small**: 4.0 (`spacingXs`)
- **Small**: 8.0 (`spacingSm`)
- **Medium**: 16.0 (`spacingMd`)
- **Large**: 24.0 (`spacingLg`)
- **Extra Large**: 32.0 (`spacingXl`)

---

## Document Version

- **Version**: 1.0.0
- **Date**: 2025-08-17
- **Author**: Architecture Team
- **Status**: Final Specification
- **Implementation Target**: 51 popup components

---

*This specification provides the foundation for consistent, professional popup theming across the Journeyman Jobs application. All developers should reference this document when implementing or modifying popup components.*
